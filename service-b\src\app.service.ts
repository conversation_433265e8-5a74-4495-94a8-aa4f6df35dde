import { Injectable, Logger } from '@nestjs/common';
import { ServiceAService } from './service-a/service-a.service';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);

  constructor(private readonly serviceAService: ServiceAService) {}

  getHello(): string {
    this.logger.log('Hello endpoint called');
    return 'Hello from Service B!';
  }

  async fetchFromServiceA(): Promise<any> {
    this.logger.log('Fetching data from Service A');
    const data = await this.serviceAService.fetchData();
    return {
      message: 'Data fetched from Service A',
      serviceAData: data,
      timestamp: new Date().toISOString()
    };
  }
}