# Monitoring and Logging MVP

A comprehensive microservices monitoring and logging infrastructure using Prometheus, Grafana, and Loki with advanced features like host metrics monitoring, RAM usage tracking, and automated health checks.

## Components

- **Service A**: HTTP service with metrics instrumentation, health endpoints, and memory monitoring
- **Service B**: Service that communicates with Service A, performs 15-minute health checks, and exposes metrics
- **Prometheus**: Collects and stores metrics from services and host machine
- **Node Exporter**: Collects host machine metrics (CPU, RAM, disk, network)
- **Grafana**: Creates visualizations and dashboards for metrics and logs
- **Loki**: Aggregates and indexes logs from all services

## Features

- **Host Metrics Monitoring**: CPU, RAM, disk, and network metrics via Node Exporter
- **Memory Usage Tracking**: Detailed memory metrics for each service
- **Health Endpoints**: Each service exposes a `/health` endpoint with detailed status information
- **Automated Health Checks**: Service B checks Service A's health every 15 minutes
- **Pre-configured Dashboards**: Ready-to-use dashboards for services and host metrics
- **Centralized Logging**: All logs collected and searchable via Loki

## Getting Started

### Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose

### Running the Project

1. Clone this repository
2. Run the following command:

```bash
docker-compose up -d
```

### Accessing the Services

- **Service A**: http://localhost:3000
- **Service A Health**: http://localhost:3000/health
- **Service A Metrics**: http://localhost:3000/metrics
- **Service B**: http://localhost:3001
- **Service B Health**: http://localhost:3001/health
- **Service B Metrics**: http://localhost:3001/metrics
- **Service B calling Service A**: http://localhost:3001/call-service-a
- **Prometheus**: http://localhost:9090
- **Prometheus Targets**: http://localhost:9090/targets
- **Node Exporter Metrics**: http://localhost:9100/metrics
- **Loki**: http://localhost:3100
- **Grafana**: http://localhost:3200 (default login: admin/admin)

## Using Grafana

Grafana is pre-configured with data sources and dashboards:

1. Access Grafana at http://localhost:3200
2. Log in with username `admin` and password `admin`
3. Navigate to Dashboards to view:
   - **Services Dashboard**: Shows service request rates, memory usage, and health status
   - **Node Exporter Dashboard**: Shows host CPU, memory, disk, and network metrics

## Testing the Monitoring Setup

### Basic Service Tests

```bash
# Test Service A
curl http://localhost:3000
curl http://localhost:3000/health
curl http://localhost:3000/metrics

# Test Service B
curl http://localhost:3001
curl http://localhost:3001/health
curl http://localhost:3001/metrics
curl http://localhost:3001/call-service-a
```

### Generate Load for Testing

```bash
# Generate requests to see metrics change
for i in {1..50}; do
  curl http://localhost:3000
  curl http://localhost:3001
  curl http://localhost:3001/call-service-a
  sleep 0.1
done
```

### View Logs in Grafana

1. Go to Explore in Grafana
2. Select Loki data source
3. Query: `{container_name="monitoring-logging-mvp-service-a-1"}`
4. Query: `{container_name="monitoring-logging-mvp-service-b-1"} |= "Checking Service A health"`

## License

[MIT](LICENSE)
