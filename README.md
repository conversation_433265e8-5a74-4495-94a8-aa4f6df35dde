# Monitoring, Logging, and Tracing MVP

A comprehensive microservices observability infrastructure using Prometheus, Grafana, Loki, and Tempo with advanced features like host metrics monitoring, RAM usage tracking, automated health checks, structured logging, and distributed tracing.

## 🚀 Recent Updates

### Fixed Issues:

- **✅ Service Dashboard Fixed** - Updated metric queries to properly display service metrics
- **✅ Logs Collection Working** - Added Winston with Loki transport for structured logging
- **✅ Distributed Tracing Added** - Implemented OpenTelemetry with Tempo backend

### New Features:

- **Distributed Tracing**: OpenTelemetry instrumentation with Tempo
- **Structured Logging**: Winston logger with Loki integration
- **Enhanced Dashboards**: Separate dashboards for metrics, logs, and traces
- **Automatic Instrumentation**: HTTP requests, database calls, and inter-service communication

## Components

- **Service A**: HTTP service with metrics instrumentation, health endpoints, structured logging, and tracing
- **Service B**: Service that communicates with Service A, performs 15-minute health checks, exposes metrics, and includes tracing
- **Prometheus**: Collects and stores metrics from services and host machine
- **Node Exporter**: Collects host machine metrics (CPU, RAM, disk, network)
- **Grafana**: Creates visualizations and dashboards for metrics, logs, and traces
- **Loki**: Aggregates and indexes structured logs from all services
- **Tempo**: Distributed tracing backend for collecting and querying traces

## Features

- **Host Metrics Monitoring**: CPU, RAM, disk, and network metrics via Node Exporter
- **Memory Usage Tracking**: Detailed memory metrics for each service
- **Health Endpoints**: Each service exposes a `/health` endpoint with detailed status information
- **Automated Health Checks**: Service B checks Service A's health every 15 minutes
- **Pre-configured Dashboards**: Ready-to-use dashboards for services, host metrics, logs, and traces
- **Centralized Logging**: Structured logs collected and searchable via Loki
- **Distributed Tracing**: End-to-end request tracing with OpenTelemetry and Tempo
- **Automatic Instrumentation**: HTTP requests, inter-service calls, and response times

## Getting Started

### Prerequisites

- Docker and Docker Compose

### Running the Project

1. Clone this repository
2. Run the following command:

```bash
docker-compose up -d
```

### Accessing the Services

- **Service A**: http://localhost:3000
- **Service A Health**: http://localhost:3000/health
- **Service A Metrics**: http://localhost:3000/metrics
- **Service B**: http://localhost:3001
- **Service B Health**: http://localhost:3001/health
- **Service B Metrics**: http://localhost:3001/metrics
- **Service B calling Service A**: http://localhost:3001/call-service-a
- **Prometheus**: http://localhost:9090
- **Prometheus Targets**: http://localhost:9090/targets
- **Node Exporter Metrics**: http://localhost:9100/metrics
- **Loki**: http://localhost:3100
- **Tempo**: http://localhost:3201
- **Grafana**: http://localhost:3200 (default login: admin/admin)

## Using Grafana

Grafana is pre-configured with data sources and dashboards:

1. Access Grafana at http://localhost:3200
2. Log in with username `admin` and password `admin`
3. Navigate to Dashboards to view:
   - **Services Dashboard**: Shows service request rates, memory usage, and health status
   - **Node Exporter Dashboard**: Shows host CPU, memory, disk, and network metrics
   - **Logs Dashboard**: Shows structured logs from all services with filtering
   - **Traces Dashboard**: Shows distributed traces and request performance

## Testing the Monitoring Setup

### Basic Service Tests

```bash
# Test Service A
curl http://localhost:3000
curl http://localhost:3000/health
curl http://localhost:3000/metrics

# Test Service B
curl http://localhost:3001
curl http://localhost:3001/health
curl http://localhost:3001/metrics
curl http://localhost:3001/call-service-a
```

### Generate Load for Testing

```bash
# Generate requests to see metrics change
for i in {1..50}; do
  curl http://localhost:3000
  curl http://localhost:3001
  curl http://localhost:3001/call-service-a
  sleep 0.1
done
```

### View Logs in Grafana

1. Go to Explore in Grafana
2. Select Loki data source
3. Query examples:
   - All service logs: `{service=~"service-a|service-b"}`
   - Service A logs: `{service="service-a"}`
   - Health check logs: `{service="service-b"} |= "health check"`
   - Error logs: `{service=~"service-a|service-b"} |= "error"`

### View Traces in Grafana

1. Go to Explore in Grafana
2. Select Tempo data source
3. Search by:
   - Service name: `service.name="service-a"`
   - Operation name: `http.method="GET"`
   - Duration: `duration > 100ms`
4. Or use the Traces Dashboard for pre-configured views

### Testing Distributed Tracing

```bash
# Generate traced requests
curl http://localhost:3001/call-service-a

# This will create traces showing:
# - Service B receiving the request
# - Service B calling Service A
# - Service A processing the request
# - Response flowing back through both services
```

## License

[MIT](LICENSE)
