{"name": "service-a", "version": "0.0.1", "description": "Service A for monitoring demo", "main": "dist/main.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main.js"}, "dependencies": {"@nestjs/common": "^9.0.0", "@nestjs/core": "^9.0.0", "@nestjs/platform-express": "^9.0.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.59.0", "@opentelemetry/exporter-trace-otlp-http": "^0.201.1", "@opentelemetry/sdk-node": "^0.201.1", "prom-client": "^14.0.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.5.5", "winston": "^3.8.1", "winston-loki": "^6.1.3"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@types/express": "^4.17.13", "@types/node": "^18.0.3", "typescript": "^4.7.4"}}