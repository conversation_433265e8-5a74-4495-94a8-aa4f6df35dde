import { Injectable } from '@nestjs/common';
import * as client from 'prom-client';

@Injectable()
export class MetricsService {
  private register: client.Registry;
  private httpRequestCounter: client.Counter;

  constructor() {
    this.register = new client.Registry();
    client.collectDefaultMetrics({ register: this.register });

    this.httpRequestCounter = new client.Counter({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'endpoint', 'status'],
      registers: [this.register],
    });
  }

  incrementHttpRequestCounter(method: string, endpoint: string, status: string): void {
    this.httpRequestCounter.inc({ method, endpoint, status });
  }

  async getMetrics(): Promise<string> {
    return this.register.metrics();
  }
}