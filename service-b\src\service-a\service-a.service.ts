import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class ServiceAService {
  private readonly logger = new Logger(ServiceAService.name);
  private readonly serviceAUrl = 'http://service-a:3000';

  async fetchData(): Promise<any> {
    try {
      this.logger.log('Calling Service A data endpoint');
      const response = await axios.get(`${this.serviceAUrl}/data`);
      return response.data;
    } catch (error) {
      this.logger.error(`Error fetching data from Service A: ${error.message}`);
      throw error;
    }
  }
}