{"name": "service-b", "version": "0.0.1", "description": "Service B for monitoring demo", "main": "dist/main.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main.js"}, "dependencies": {"@nestjs/common": "^9.0.0", "@nestjs/core": "^9.0.0", "@nestjs/platform-express": "^9.0.0", "axios": "^1.1.3", "prom-client": "^14.0.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.5.5", "winston": "^3.8.1"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@types/express": "^4.17.13", "@types/node": "^18.0.3", "typescript": "^4.7.4"}}