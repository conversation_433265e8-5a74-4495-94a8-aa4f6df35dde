version: '3.8'

services:
  service-a:
    build: ./service-a
    ports:
      - "3000:3000"
    networks:
      - monitoring-network
    volumes:
      - ./service-a:/app
      - /app/node_modules
    healthcheck:
      test: [ "CMD", "wget", "--spider", "-q", "http://localhost:3000/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=service-a"

  service-b:
    build: ./service-b
    ports:
      - "3001:3001"
    networks:
      - monitoring-network
    volumes:
      - ./service-b:/app
      - /app/node_modules
    depends_on:
      - service-a
    healthcheck:
      test: [ "CMD", "wget", "--spider", "-q", "http://localhost:3001/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=service-b"

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - monitoring-network

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    networks:
      - monitoring-network

  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
    networks:
      - monitoring-network
    command: -config.file=/etc/loki/local-config.yaml

  promtail:
    image: grafana/promtail:latest
    volumes:
      - ./promtail/promtail.yml:/etc/promtail/config.yml
      - /var/run/docker.sock:/var/run/docker.sock
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - monitoring-network
    depends_on:
      - loki

  tempo:
    image: grafana/tempo:latest
    command: [ "-config.file=/etc/tempo.yml" ]
    volumes:
      - ./tempo/tempo.yml:/etc/tempo.yml
    ports:
      - "3201:3200" # tempo
      - "4317:4317" # otlp grpc
      - "4318:4318" # otlp http
    networks:
      - monitoring-network

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3200:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_SECURITY_ADMIN_USER=admin
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning
    networks:
      - monitoring-network
    depends_on:
      - prometheus
      - loki
      - tempo

networks:
  monitoring-network:
    driver: bridge
