// Simple Service B
const http = require("http");

const server = http.createServer(async (req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

  // Handle different endpoints
  if (req.url === "/health") {
    res.statusCode = 200;
    res.setHeader("Content-Type", "application/json");
    const healthData = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      service: "service-b"
    };
    res.end(JSON.stringify(healthData));
    return;
  }

  if (req.url === "/metrics") {
    res.statusCode = 200;
    res.setHeader("Content-Type", "text/plain");
    res.end("# Simple metrics\nservice_b_up 1\n");
    return;
  }

  // Regular response
  res.statusCode = 200;
  res.setHeader("Content-Type", "application/json");
  res.end(JSON.stringify({
    message: "Hello from Service B!",
    timestamp: new Date().toISOString(),
    service: "service-b"
  }));
});

server.listen(3001, () => {
  console.log("Service B started successfully on port 3001");
  console.log("Health endpoint: http://localhost:3001/health");
  console.log("Metrics endpoint: http://localhost:3001/metrics");
});
