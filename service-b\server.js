// Initialize OpenTelemetry first
const { NodeSDK } = require("@opentelemetry/sdk-node");
const {
  getNodeAutoInstrumentations,
} = require("@opentelemetry/auto-instrumentations-node");
const {
  OTLPTraceExporter,
} = require("@opentelemetry/exporter-trace-otlp-http");

const sdk = new NodeSDK({
  traceExporter: new OTLPTraceExporter({
    url: "http://tempo:4318/v1/traces",
  }),
  instrumentations: [getNodeAutoInstrumentations()],
  serviceName: "service-b",
});

sdk.start();

const http = require("http");
const prometheus = require("prom-client");
const axios = require("axios");
const os = require("os");
const winston = require("winston");
const LokiTransport = require("winston-loki");

// Configure <PERSON> logger with Loki transport
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: "service-b" },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
    new LokiTransport({
      host: "http://loki:3100",
      labels: { service: "service-b" },
      json: true,
      format: winston.format.json(),
      replaceTimestamp: true,
      onConnectionError: (err) => console.error("Loki connection error:", err),
    }),
  ],
});

// Create a Registry to register the metrics
const register = new prometheus.Registry();

// Add a default label which is added to all metrics
prometheus.collectDefaultMetrics({ register });

// Create metrics
const requestCounter = new prometheus.Counter({
  name: "service_b_requests_total",
  help: "Total number of requests to Service B",
  labelNames: ["method", "endpoint"],
});

const memoryGauge = new prometheus.Gauge({
  name: "service_b_memory_usage_bytes",
  help: "Memory usage of Service B in bytes",
});

const healthGauge = new prometheus.Gauge({
  name: "service_b_health_status",
  help: "Health status of Service B (1 = healthy, 0 = unhealthy)",
});

const serviceAHealthGauge = new prometheus.Gauge({
  name: "service_a_health_check_status",
  help: "Health status of Service A as checked by Service B (1 = healthy, 0 = unhealthy)",
});

const serviceAResponseTimeGauge = new prometheus.Gauge({
  name: "service_a_response_time_seconds",
  help: "Response time of Service A in seconds",
});

// Register all metrics
register.registerMetric(requestCounter);
register.registerMetric(memoryGauge);
register.registerMetric(healthGauge);
register.registerMetric(serviceAHealthGauge);
register.registerMetric(serviceAResponseTimeGauge);

// Set initial health status to healthy
healthGauge.set(1);
serviceAHealthGauge.set(1);

// Update memory usage every 15 seconds
setInterval(() => {
  const memoryUsage = process.memoryUsage();
  memoryGauge.set(memoryUsage.rss);
}, 15000);

// Check Service A health every 15 minutes (900000 ms)
const checkServiceAHealth = async () => {
  logger.info("Starting health check for Service A");

  try {
    const startTime = Date.now();
    const response = await axios.get("http://service-a:3000/health", {
      timeout: 5000,
    });
    const responseTime = (Date.now() - startTime) / 1000; // Convert to seconds

    if (response.status === 200 && response.data.status === "healthy") {
      logger.info("Service A health check successful", {
        status: "healthy",
        responseTime: responseTime,
        timestamp: new Date().toISOString(),
      });
      serviceAHealthGauge.set(1);
      serviceAResponseTimeGauge.set(responseTime);
    } else {
      logger.error("Service A health check failed - unexpected status", {
        status: response.data.status,
        responseTime: responseTime,
        timestamp: new Date().toISOString(),
      });
      serviceAHealthGauge.set(0);
    }
  } catch (error) {
    logger.error("Service A health check failed - connection error", {
      error: error.message,
      timestamp: new Date().toISOString(),
    });
    serviceAHealthGauge.set(0);
    serviceAResponseTimeGauge.set(0);
  }
};

// Run health check immediately and then every 15 minutes
checkServiceAHealth();
setInterval(checkServiceAHealth, 900000); // 15 minutes

// Create the HTTP server
const server = http.createServer(async (req, res) => {
  const startTime = Date.now();

  // Log incoming request
  logger.info("Incoming request", {
    method: req.method,
    url: req.url,
    userAgent: req.headers["user-agent"],
    timestamp: new Date().toISOString(),
  });

  // Increment the counter
  requestCounter.inc({ method: req.method, endpoint: req.url });

  if (req.url === "/metrics") {
    logger.debug("Serving metrics endpoint");
    // Return all metrics in Prometheus exposition format
    res.setHeader("Content-Type", register.contentType);
    res.end(await register.metrics());
    return;
  }

  if (req.url === "/health") {
    logger.debug("Health check requested");
    // Health check endpoint
    res.statusCode = 200;
    res.setHeader("Content-Type", "application/json");
    const healthData = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      memory: process.memoryUsage(),
      uptime: process.uptime(),
      hostname: os.hostname(),
      serviceAStatus: serviceAHealthGauge.get() === 1 ? "healthy" : "unhealthy",
    };
    res.end(JSON.stringify(healthData));

    logger.info("Health check completed", {
      status: "healthy",
      serviceAStatus: healthData.serviceAStatus,
      responseTime: Date.now() - startTime,
    });
    return;
  }

  if (req.url === "/call-service-a") {
    logger.info("Calling Service A");
    try {
      const response = await axios.get("http://service-a:3000");
      res.statusCode = 200;
      res.setHeader("Content-Type", "text/plain");
      res.end(`Service B received from Service A: ${response.data}`);

      logger.info("Service A call successful", {
        responseTime: Date.now() - startTime,
        responseData: response.data,
      });
    } catch (error) {
      logger.error("Service A call failed", {
        error: error.message,
        responseTime: Date.now() - startTime,
      });
      res.statusCode = 500;
      res.setHeader("Content-Type", "text/plain");
      res.end(`Error calling Service A: ${error.message}`);
    }
    return;
  }

  // Regular response
  logger.info("Serving main endpoint");
  res.statusCode = 200;
  res.setHeader("Content-Type", "text/plain");
  res.end("Hello from Service B");

  // Log response
  logger.info("Request completed", {
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    responseTime: Date.now() - startTime,
  });
});

// Start the server
server.listen(3001, () => {
  const message = "Service B started successfully";
  console.log(message);
  console.log("Health endpoint available at http://localhost:3001/health");
  console.log("Metrics endpoint available at http://localhost:3001/metrics");

  logger.info(message, {
    port: 3001,
    endpoints: {
      health: "/health",
      metrics: "/metrics",
      main: "/",
      callServiceA: "/call-service-a",
    },
    timestamp: new Date().toISOString(),
  });
});
