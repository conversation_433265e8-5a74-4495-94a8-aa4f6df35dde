const http = require("http");
const prometheus = require("prom-client");
const axios = require("axios");
const os = require("os");

// Create a Registry to register the metrics
const register = new prometheus.Registry();

// Add a default label which is added to all metrics
prometheus.collectDefaultMetrics({ register });

// Create metrics
const requestCounter = new prometheus.Counter({
  name: "service_b_requests_total",
  help: "Total number of requests to Service B",
  labelNames: ["method", "endpoint"],
});

const memoryGauge = new prometheus.Gauge({
  name: "service_b_memory_usage_bytes",
  help: "Memory usage of Service B in bytes",
});

const healthGauge = new prometheus.Gauge({
  name: "service_b_health_status",
  help: "Health status of Service B (1 = healthy, 0 = unhealthy)",
});

const serviceAHealthGauge = new prometheus.Gauge({
  name: "service_a_health_check_status",
  help: "Health status of Service A as checked by Service B (1 = healthy, 0 = unhealthy)",
});

const serviceAResponseTimeGauge = new prometheus.Gauge({
  name: "service_a_response_time_seconds",
  help: "Response time of Service A in seconds",
});

// Register all metrics
register.registerMetric(requestCounter);
register.registerMetric(memoryGauge);
register.registerMetric(healthGauge);
register.registerMetric(serviceAHealthGauge);
register.registerMetric(serviceAResponseTimeGauge);

// Set initial health status to healthy
healthGauge.set(1);
serviceAHealthGauge.set(1);

// Update memory usage every 15 seconds
setInterval(() => {
  const memoryUsage = process.memoryUsage();
  memoryGauge.set(memoryUsage.rss);
}, 15000);

// Check Service A health every 15 minutes (900000 ms)
const checkServiceAHealth = async () => {
  console.log(`[${new Date().toISOString()}] Checking Service A health...`);

  try {
    const startTime = Date.now();
    const response = await axios.get("http://service-a:3000/health", {
      timeout: 5000,
    });
    const responseTime = (Date.now() - startTime) / 1000; // Convert to seconds

    if (response.status === 200 && response.data.status === "healthy") {
      console.log(
        `[${new Date().toISOString()}] Service A is healthy. Response time: ${responseTime}s`
      );
      serviceAHealthGauge.set(1);
      serviceAResponseTimeGauge.set(responseTime);
    } else {
      console.error(
        `[${new Date().toISOString()}] Service A returned unexpected status: ${
          response.data.status
        }`
      );
      serviceAHealthGauge.set(0);
    }
  } catch (error) {
    console.error(
      `[${new Date().toISOString()}] Error checking Service A health: ${
        error.message
      }`
    );
    serviceAHealthGauge.set(0);
    serviceAResponseTimeGauge.set(0);
  }
};

// Run health check immediately and then every 15 minutes
checkServiceAHealth();
setInterval(checkServiceAHealth, 900000); // 15 minutes

// Create the HTTP server
const server = http.createServer(async (req, res) => {
  // Increment the counter
  requestCounter.inc({ method: req.method, endpoint: req.url });

  if (req.url === "/metrics") {
    // Return all metrics in Prometheus exposition format
    res.setHeader("Content-Type", register.contentType);
    res.end(await register.metrics());
    return;
  }

  if (req.url === "/health") {
    // Health check endpoint
    res.statusCode = 200;
    res.setHeader("Content-Type", "application/json");
    res.end(
      JSON.stringify({
        status: "healthy",
        timestamp: new Date().toISOString(),
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        hostname: os.hostname(),
        serviceAStatus:
          serviceAHealthGauge.get() === 1 ? "healthy" : "unhealthy",
      })
    );
    return;
  }

  if (req.url === "/call-service-a") {
    try {
      const response = await axios.get("http://service-a:3000");
      res.statusCode = 200;
      res.setHeader("Content-Type", "text/plain");
      res.end(`Service B received from Service A: ${response.data}`);
    } catch (error) {
      res.statusCode = 500;
      res.setHeader("Content-Type", "text/plain");
      res.end(`Error calling Service A: ${error.message}`);
    }
    return;
  }

  // Regular response
  res.statusCode = 200;
  res.setHeader("Content-Type", "text/plain");
  res.end("Hello from Service B");
});

// Start the server
server.listen(3001, () => {
  console.log("Service B running on port 3001");
  console.log("Health endpoint available at http://localhost:3001/health");
  console.log("Metrics endpoint available at http://localhost:3001/metrics");
});
