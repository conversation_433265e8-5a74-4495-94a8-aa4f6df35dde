import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);

  getHello(): string {
    this.logger.log('Hello endpoint called');
    return 'Hello from Service A!';
  }

  getData(): any {
    this.logger.log('Data endpoint called');
    return {
      message: 'Data from Service A',
      timestamp: new Date().toISOString(),
      random: Math.floor(Math.random() * 100)
    };
  }
}