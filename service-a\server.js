// Simple Service A
const http = require("http");

const server = http.createServer(async (req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

  // Handle different endpoints
  if (req.url === "/health") {
    res.statusCode = 200;
    res.setHeader("Content-Type", "application/json");
    const healthData = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      service: "service-a",
    };
    res.end(JSON.stringify(healthData));
    return;
  }

  if (req.url === "/metrics") {
    res.statusCode = 200;
    res.setHeader("Content-Type", "text/plain");
    res.end("# Simple metrics\nservice_a_up 1\n");
    return;
  }

  // Regular response
  res.statusCode = 200;
  res.setHeader("Content-Type", "application/json");
  res.end(
    JSON.stringify({
      message: "Hello from Service A!",
      timestamp: new Date().toISOString(),
      service: "service-a",
    })
  );
});

server.listen(3000, () => {
  console.log("Service A started successfully on port 3000");
  console.log("Health endpoint: http://localhost:3000/health");
  console.log("Metrics endpoint: http://localhost:3000/metrics");
});
