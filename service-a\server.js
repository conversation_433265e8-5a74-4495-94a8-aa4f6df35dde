const http = require("http");
const prometheus = require("prom-client");
const os = require("os");

const register = new prometheus.Registry();

prometheus.collectDefaultMetrics({ register });

// Create a counter metric for requests
const requestCounter = new prometheus.Counter({
  name: "service_a_requests_total",
  help: "Total number of requests to Service A",
  labelNames: ["method", "endpoint"],
});

// Create a gauge for memory usage
const memoryGauge = new prometheus.Gauge({
  name: "service_a_memory_usage_bytes",
  help: "Memory usage of Service A in bytes",
});

// Create a gauge for health status
const healthGauge = new prometheus.Gauge({
  name: "service_a_health_status",
  help: "Health status of Service A (1 = healthy, 0 = unhealthy)",
});

// Register all metrics
register.registerMetric(requestCounter);
register.registerMetric(memoryGauge);
register.registerMetric(healthGauge);

// Set initial health status to healthy
healthGauge.set(1);

// Update memory usage every 15 seconds
setInterval(() => {
  const memoryUsage = process.memoryUsage();
  memoryGauge.set(memoryUsage.rss);
}, 15000);

const server = http.createServer(async (req, res) => {
  // Increment the request counter
  requestCounter.inc({ method: req.method, endpoint: req.url });

  // Handle different endpoints
  if (req.url === "/metrics") {
    // Return all metrics in Prometheus exposition format
    res.setHeader("Content-Type", register.contentType);
    res.end(await register.metrics());
    return;
  }

  if (req.url === "/health") {
    // Health check endpoint
    res.statusCode = 200;
    res.setHeader("Content-Type", "application/json");
    res.end(
      JSON.stringify({
        status: "healthy",
        timestamp: new Date().toISOString(),
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        hostname: os.hostname(),
      })
    );
    return;
  }

  // Regular response
  res.statusCode = 200;
  res.setHeader("Content-Type", "text/plain");
  res.end("Hello from Service A");
});

server.listen(3000, () => {
  console.log("Service A running on port 3000");
  console.log("Health endpoint available at http://localhost:3000/health");
  console.log("Metrics endpoint available at http://localhost:3000/metrics");
});
