// Initialize OpenTelemetry first
const { NodeSDK } = require("@opentelemetry/sdk-node");
const {
  getNodeAutoInstrumentations,
} = require("@opentelemetry/auto-instrumentations-node");
const {
  OTLPTraceExporter,
} = require("@opentelemetry/exporter-trace-otlp-http");

const sdk = new NodeSDK({
  traceExporter: new OTLPTraceExporter({
    url: "http://tempo:4318/v1/traces",
  }),
  instrumentations: [getNodeAutoInstrumentations()],
  serviceName: "service-a",
});

sdk.start();

const http = require("http");
const prometheus = require("prom-client");
const os = require("os");
const winston = require("winston");
const LokiTransport = require("winston-loki");

// Configure Winston logger with Loki transport
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: "service-a" },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
    new LokiTransport({
      host: "http://loki:3100",
      labels: { service: "service-a" },
      json: true,
      format: winston.format.json(),
      replaceTimestamp: true,
      onConnectionError: (err) => console.error("Loki connection error:", err),
    }),
  ],
});

const register = new prometheus.Registry();

prometheus.collectDefaultMetrics({ register });

// Create a counter metric for requests
const requestCounter = new prometheus.Counter({
  name: "service_a_requests_total",
  help: "Total number of requests to Service A",
  labelNames: ["method", "endpoint"],
});

// Create a gauge for memory usage
const memoryGauge = new prometheus.Gauge({
  name: "service_a_memory_usage_bytes",
  help: "Memory usage of Service A in bytes",
});

// Create a gauge for health status
const healthGauge = new prometheus.Gauge({
  name: "service_a_health_status",
  help: "Health status of Service A (1 = healthy, 0 = unhealthy)",
});

// Register all metrics
register.registerMetric(requestCounter);
register.registerMetric(memoryGauge);
register.registerMetric(healthGauge);

// Set initial health status to healthy
healthGauge.set(1);

// Update memory usage every 15 seconds
setInterval(() => {
  const memoryUsage = process.memoryUsage();
  memoryGauge.set(memoryUsage.rss);
}, 15000);

const server = http.createServer(async (req, res) => {
  const startTime = Date.now();

  // Log incoming request
  logger.info("Incoming request", {
    method: req.method,
    url: req.url,
    userAgent: req.headers["user-agent"],
    timestamp: new Date().toISOString(),
  });

  // Increment the request counter
  requestCounter.inc({ method: req.method, endpoint: req.url });

  // Handle different endpoints
  if (req.url === "/metrics") {
    logger.debug("Serving metrics endpoint");
    // Return all metrics in Prometheus exposition format
    res.setHeader("Content-Type", register.contentType);
    res.end(await register.metrics());
    return;
  }

  if (req.url === "/health") {
    logger.debug("Health check requested");
    // Health check endpoint
    res.statusCode = 200;
    res.setHeader("Content-Type", "application/json");
    const healthData = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      memory: process.memoryUsage(),
      uptime: process.uptime(),
      hostname: os.hostname(),
    };
    res.end(JSON.stringify(healthData));

    logger.info("Health check completed", {
      status: "healthy",
      responseTime: Date.now() - startTime,
    });
    return;
  }

  // Regular response
  logger.info("Serving main endpoint");
  res.statusCode = 200;
  res.setHeader("Content-Type", "text/plain");
  res.end("Hello from Service A");

  // Log response
  logger.info("Request completed", {
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    responseTime: Date.now() - startTime,
  });
});

server.listen(3000, () => {
  const message = "Service A started successfully";
  console.log(message);
  console.log("Health endpoint available at http://localhost:3000/health");
  console.log("Metrics endpoint available at http://localhost:3000/metrics");

  logger.info(message, {
    port: 3000,
    endpoints: {
      health: "/health",
      metrics: "/metrics",
      main: "/",
    },
    timestamp: new Date().toISOString(),
  });
});
