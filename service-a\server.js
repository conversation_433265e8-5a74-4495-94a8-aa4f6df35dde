// Simple Service A with basic tracing
const http = require("http");

// Simple trace simulation (not real OpenTelemetry)
function generateTraceId() {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

function sendSimpleTrace(operation, duration) {
  const trace = {
    traceID: generateTraceId(),
    spanID: generateTraceId().substring(0, 8),
    operationName: operation,
    startTime: Date.now() * 1000, // microseconds
    duration: duration * 1000, // microseconds
    tags: {
      service: "service-a",
      http_method: "GET"
    }
  };

  // In a real setup, this would send to Tempo
  console.log(`TRACE: ${JSON.stringify(trace)}`);
}

const server = http.createServer(async (req, res) => {
  const startTime = Date.now();
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

  // Handle different endpoints
  if (req.url === "/health") {
    const duration = Date.now() - startTime;
    sendSimpleTrace("health_check", duration);

    res.statusCode = 200;
    res.setHeader("Content-Type", "application/json");
    const healthData = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      service: "service-a",
    };
    res.end(JSON.stringify(healthData));
    return;
  }

  if (req.url === "/metrics") {
    res.statusCode = 200;
    res.setHeader("Content-Type", "text/plain");
    res.end("# Simple metrics\nservice_a_up 1\n");
    return;
  }

  // Regular response
  res.statusCode = 200;
  res.setHeader("Content-Type", "application/json");
  res.end(
    JSON.stringify({
      message: "Hello from Service A!",
      timestamp: new Date().toISOString(),
      service: "service-a",
    })
  );
});

server.listen(3000, () => {
  console.log("Service A started successfully on port 3000");
  console.log("Health endpoint: http://localhost:3000/health");
  console.log("Metrics endpoint: http://localhost:3000/metrics");
});
